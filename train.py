#    Copyright 2023 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>
#
#    Licensed under the Apache License, Version 2.0 (the "License");
#    you may not use this file except in compliance with the License.
#    You may obtain a copy of the License at
#
#        http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS,
#    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#    See the License for the specific language governing permissions and
#    limitations under the License.

import os
import copy
import logging
from dataclasses import dataclass, field
from typing import Optional, Dict, Sequence
import numpy as np

from setproctitle import setproctitle
import torch
import transformers
from torch.utils.data import Dataset
from transformers import Trainer
from datasets import load_from_disk, concatenate_datasets


IGNORE_INDEX = -100
DEFAULT_PAD_TOKEN = "[PAD]"
DEFAULT_EOS_TOKEN = "</s>"
DEFAULT_BOS_TOKEN = "</s>"
DEFAULT_UNK_TOKEN = "</s>"


@dataclass
class ModelArguments:
    model_name_or_path: Optional[str] = field(default="llama2")
    output_name: Optional[str] = field(default="ddd")


@dataclass
class DataArguments:
    data_name: Optional[str] = field(
        default="/data_x/junkim100/projects/KULLM/finetune/data/original_GAIR_LIMO_train_817.jsonl"
    )
    max_length: int = field(
        default=16384,
        metadata={
            "help": "Maximum sequence length. Sequences will be right padded (and possibly truncated)."
        },
    )


@dataclass
class TrainingArguments(transformers.TrainingArguments):
    cache_dir: Optional[str] = field(default=None)
    optim: str = field(
        default="adamw_torch",
        metadata={"help": "adamw_torch, paged_adamw_32bit, adamw_bnb_8bit"},
    )
    report_to: str = field(default=None, metadata={"help": "wandb"})
    wb_name: str = field(default="my_run_name", metadata={"help": "wandb name"})
    wb_project: str = field(default="my_project", metadata={"help": "wandb project"})
    proctitle: str = field(default="junkim100", metadata={"help": "proctitle"})

    bf16: bool = field(default=True, metadata={"help": "default: False"})
    tf32: bool = field(default=True, metadata={"help": "default: None"})
    # fsdp: str = field(default='full_shard auto_wrap')

    num_train_epochs: int = field(default=4)
    per_device_eval_batch_size: int = field(default=1)
    per_device_train_batch_size: int = field(default=1)
    gradient_accumulation_steps: int = field(default=16)

    logging_steps: int = field(default=10)
    # save_steps: int = field(default=100)
    # save_total_limit: int = field(default=2)

    learning_rate: float = field(default=1e-5)
    max_grad_norm: float = field(default=0.3)
    lr_scheduler_type: str = field(default="cosine")
    warmup_ratio: float = field(default=0.03)
    weight_decay: float = field(default=0.0)

    deepspeed: str = field(default="./deepspeed3.json")


def smart_tokenizer_and_embedding_resize(
    special_tokens_dict: Dict,
    tokenizer: transformers.PreTrainedTokenizer,
    model: transformers.PreTrainedModel,
):
    """Resize tokenizer and embedding.

    Note: This is the unoptimized version that may make your embedding size not be divisible by 64.
    """
    num_new_tokens = tokenizer.add_special_tokens(special_tokens_dict)
    model.resize_token_embeddings(len(tokenizer))

    if num_new_tokens > 0:
        input_embeddings = model.get_input_embeddings().weight.data
        output_embeddings = model.get_output_embeddings().weight.data

        input_embeddings_avg = input_embeddings[:-num_new_tokens].mean(
            dim=0, keepdim=True
        )
        output_embeddings_avg = output_embeddings[:-num_new_tokens].mean(
            dim=0, keepdim=True
        )

        input_embeddings[-num_new_tokens:] = input_embeddings_avg
        output_embeddings[-num_new_tokens:] = output_embeddings_avg


def make_supervised(data, tokenizer, max_length):
    inputs_all, labels_all = [], []
    cnt = 0
    for d in data:
        # 근데 거의 대부분은 system_prompt 없이 할거니까.. 그냥 하나로 이어서 사용하기
        source = [{"role": "user", "content": d["question"]}]
        labels = [
            {"role": "user", "content": d["question"]},
            {"role": "assistant", "content": d["solution"]},
        ]

        source = tokenizer.apply_chat_template(source, return_tensors="pt")[0]
        labels = tokenizer.apply_chat_template(labels, return_tensors="pt")[0]

        if len(labels) >= max_length:
            cnt += 1
            labels = labels[:max_length]
            # continue

        inputs = copy.deepcopy(labels)
        labels[: len(source)] = IGNORE_INDEX

        inputs_all.append(inputs)
        labels_all.append(labels)
    # logging.warning(f"skipped by max len: {cnt}")

    logging.warning(f"mean_token_length: {np.mean([len(item) for item in labels_all])}")
    logging.warning(f"min_token_length: {np.min([len(item) for item in labels_all])}")
    logging.warning(f"max_token_length: {np.max([len(item) for item in labels_all])}")
    logging.warning(f"revised by max len: {cnt} out of {len(labels_all)}")
    return inputs_all, labels_all


class SupervisedDataset(Dataset):
    """Dataset for supervised fine-tuning."""

    def __init__(self, data_name, tokenizer, max_length: int):
        super(SupervisedDataset, self).__init__()
        logging.warning("Loading data...")

        data_all = load_from_disk(data_name)

        logging.warning("Data Loaded...")
        inputs_all, labels_all = make_supervised(
            data=data_all, tokenizer=tokenizer, max_length=max_length
        )
        logging.warning("Data Prepared...")
        self.input_ids = inputs_all
        self.labels = labels_all

    def __len__(self):
        return len(self.input_ids)

    def __getitem__(self, i) -> Dict[str, torch.Tensor]:
        return dict(input_ids=self.input_ids[i], labels=self.labels[i])


@dataclass
class DataCollatorForSupervisedDataset(object):
    """Collate examples for supervised fine-tuning."""

    tokenizer: transformers.PreTrainedTokenizer

    def __call__(self, instances: Sequence[Dict]) -> Dict[str, torch.Tensor]:
        input_ids, labels = tuple(
            [instance[key] for instance in instances] for key in ("input_ids", "labels")
        )
        input_ids = torch.nn.utils.rnn.pad_sequence(
            input_ids, batch_first=True, padding_value=self.tokenizer.pad_token_id
        )
        labels = torch.nn.utils.rnn.pad_sequence(
            labels, batch_first=True, padding_value=IGNORE_INDEX
        )
        return dict(
            input_ids=input_ids,
            labels=labels,
            attention_mask=input_ids.ne(self.tokenizer.pad_token_id),
        )


def make_supervised_data_module(
    tokenizer: transformers.PreTrainedTokenizer, data_args
) -> Dict:
    """Make dataset and collator for supervised fine-tuning."""
    train_dataset = SupervisedDataset(
        data_name=data_args.data_name,
        tokenizer=tokenizer,
        max_length=data_args.max_length,
    )
    data_collator = DataCollatorForSupervisedDataset(tokenizer=tokenizer)
    return dict(
        train_dataset=train_dataset, eval_dataset=None, data_collator=data_collator
    )


def train(model_args, data_args, training_args):
    setproctitle(training_args.proctitle)

    model_name_or_path = {
        "llama3.1": "/data/hyeon/2.chekcpoints/LLMs/Meta-Llama-3.1-8B-Instruct",
        "qwen2.5_7B": "/data/hyeon/2.chekcpoints/LLMs/Qwen2.5-7B-Instruct",
    }[model_args.model_name_or_path]

    # model = transformers.AutoModelForCausalLM.from_pretrained(
    #     model_name_or_path,
    #     attn_implementation="flash_attention_2",
    #     torch_dtype="auto"
    # )
    model = transformers.AutoModelForCausalLM.from_pretrained(
        model_name_or_path, torch_dtype="auto"
    )

    tokenizer = transformers.AutoTokenizer.from_pretrained(
        model_name_or_path,
        cache_dir=training_args.cache_dir,
        model_max_length=data_args.max_length,
        padding_side="right",
        use_fast=False,
    )

    if tokenizer.pad_token is None:
        smart_tokenizer_and_embedding_resize(
            special_tokens_dict=dict(pad_token=DEFAULT_PAD_TOKEN),
            tokenizer=tokenizer,
            model=model,
        )

    training_args.gradient_checkpointing = True
    data_module = make_supervised_data_module(tokenizer=tokenizer, data_args=data_args)
    trainer = Trainer(
        model=model, tokenizer=tokenizer, args=training_args, **data_module
    )
    trainer.train()
    trainer.save_state()
    trainer.save_model(training_args.output_dir)


if __name__ == "__main__":
    parser = transformers.HfArgumentParser(
        (ModelArguments, DataArguments, TrainingArguments)
    )
    model_args, data_args, training_args = parser.parse_args_into_dataclasses()

    training_args.fsdp = []
    training_args.fsdp_min_num_params = 0
    training_args.fsdp_transformer_layer_cls_to_wrap = None

    logging.warning(f"trining_args:\n{training_args}")
    training_args.output_dir = os.path.join(
        f"./output", model_args.model_name_or_path, model_args.output_name
    )

    os.environ["WANDB_PROJECT"] = training_args.wb_project
    os.environ["WANDB_LOG_MODEL"] = training_args.wb_name

    train(model_args, data_args, training_args)
    torch.cuda.empty_cache()
