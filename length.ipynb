{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["original_min: 902, original_max: 15205, original_mean: 4491.515299877601\n", "code_switch_min: 902, code_switch_max: 15205, code_switch_mean: 4491.515299877601\n"]}], "source": ["# call the Qwen/Qwen2.5-7B-Instruct tokenizer and measure the token length of ./data/file_name.jsonl file's question and solution combined\n", "\n", "import json\n", "import os\n", "import numpy as np\n", "from transformers import AutoTokenizer\n", "import torch\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(\n", "    \"/data/hyeon/2.chekcpoints/LLMs/Qwen2.5-7B-Instruct\",\n", "    cache_dir=None,\n", "    model_max_length=16384,\n", "    padding_side=\"right\",\n", "    use_fast=False,\n", ")\n", "\n", "original_path = \"data/code_switched_GAIR_LIMO_train_817.jsonl\"\n", "code_switch_path = \"data/code_switched_GAIR_LIMO_train_817.jsonl\"\n", "\n", "original_data = []\n", "with open(original_path, \"r\") as f:\n", "    for line in f:\n", "        original_data.append(json.loads(line))\n", "\n", "code_switch_data = []\n", "with open(code_switch_path, \"r\") as f:\n", "    for line in f:\n", "        code_switch_data.append(json.loads(line))\n", "\n", "def get_token_length(data):\n", "    # get min max mean token length of question and solution combined\n", "    token_lengths = []\n", "    for d in data:\n", "        source = [{\"role\": \"user\", \"content\": d['question']}]\n", "        labels = [{\"role\": \"user\", \"content\": d['question']}, {\"role\": \"assistant\", \"content\": d['solution']}]\n", "\n", "        source = tokenizer.apply_chat_template(source, return_tensors=\"pt\")[0]\n", "        labels = tokenizer.apply_chat_template(labels, return_tensors=\"pt\")[0]\n", "\n", "        token_lengths.append(len(labels))\n", "    return np.min(token_lengths), np.max(token_lengths), np.mean(token_lengths)\n", "\n", "original_min, original_max, original_mean = get_token_length(original_data)\n", "code_switch_min, code_switch_max, code_switch_mean = get_token_length(code_switch_data)\n", "\n", "print(f\"original_min: {original_min}, original_max: {original_max}, original_mean: {original_mean}\")\n", "print(f\"code_switch_min: {code_switch_min}, code_switch_max: {code_switch_max}, code_switch_mean: {code_switch_mean}\")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}