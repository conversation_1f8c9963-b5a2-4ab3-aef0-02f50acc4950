[2025-06-25 14:50:14,799] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-25 14:50:17,036] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
[2025-06-25 14:50:17,407] [WARNING] [runner.py:220:fetch_hostfile] Unable to find hostfile, will proceed with training with local resources only.
Detected VISIBLE_DEVICES=4,5,6,7 but ignoring it because one or several of --include/--exclude/--num_gpus/--num_nodes cl args were used. If you want to use CUDA_VISIBLE_DEVICES don't pass any of these arguments to deepspeed.
[2025-06-25 14:50:17,407] [INFO] [runner.py:610:main] cmd = /mnt/raid6/junkim100/miniconda3/envs/limo/bin/python3.10 -u -m deepspeed.launcher.launch --world_info=eyJsb2NhbGhvc3QiOiBbMCwgMSwgMiwgM119 --master_addr=127.0.0.1 --master_port=49056 --enable_each_rank_log=None train.py --proctitle junkim100 --model_name_or_path qwen2.5_7B --data_name /data_x/junkim100/projects/KULLM/finetune/data/code_switched_GAIR_LIMO_train_817.jsonl --wb_project kullm-pro --wb_name code-switch-limo --output_name code-switch-limo --max_length 16384 --num_train_epochs 2 --per_device_train_batch_size 1 --per_device_eval_batch_size 1 --gradient_accumulation_steps 1 --save_only_model --learning_rate 1e-05 --weight_decay 0. --warmup_ratio 0. --lr_scheduler_type cosine --bf16 True --logging_steps 1 --fsdp full_shard auto_wrap --tf32 True
[2025-06-25 14:50:18,838] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-25 14:50:21,026] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
[2025-06-25 14:50:21,403] [INFO] [launch.py:146:main] WORLD INFO DICT: {'localhost': [0, 1, 2, 3]}
[2025-06-25 14:50:21,403] [INFO] [launch.py:152:main] nnodes=1, num_local_procs=4, node_rank=0
[2025-06-25 14:50:21,403] [INFO] [launch.py:163:main] global_rank_mapping=defaultdict(<class 'list'>, {'localhost': [0, 1, 2, 3]})
[2025-06-25 14:50:21,403] [INFO] [launch.py:164:main] dist_world_size=4
[2025-06-25 14:50:21,403] [INFO] [launch.py:168:main] Setting CUDA_VISIBLE_DEVICES=0,1,2,3
[2025-06-25 14:50:21,407] [INFO] [launch.py:256:main] process 838151 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/limo/bin/python3.10', '-u', 'train.py', '--local_rank=0', '--proctitle', 'junkim100', '--model_name_or_path', 'qwen2.5_7B', '--data_name', '/data_x/junkim100/projects/KULLM/finetune/data/code_switched_GAIR_LIMO_train_817.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'code-switch-limo', '--output_name', 'code-switch-limo', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-05', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--logging_steps', '1', '--fsdp', 'full_shard auto_wrap', '--tf32', 'True']
[2025-06-25 14:50:21,409] [INFO] [launch.py:256:main] process 838152 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/limo/bin/python3.10', '-u', 'train.py', '--local_rank=1', '--proctitle', 'junkim100', '--model_name_or_path', 'qwen2.5_7B', '--data_name', '/data_x/junkim100/projects/KULLM/finetune/data/code_switched_GAIR_LIMO_train_817.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'code-switch-limo', '--output_name', 'code-switch-limo', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-05', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--logging_steps', '1', '--fsdp', 'full_shard auto_wrap', '--tf32', 'True']
[2025-06-25 14:50:21,411] [INFO] [launch.py:256:main] process 838153 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/limo/bin/python3.10', '-u', 'train.py', '--local_rank=2', '--proctitle', 'junkim100', '--model_name_or_path', 'qwen2.5_7B', '--data_name', '/data_x/junkim100/projects/KULLM/finetune/data/code_switched_GAIR_LIMO_train_817.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'code-switch-limo', '--output_name', 'code-switch-limo', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-05', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--logging_steps', '1', '--fsdp', 'full_shard auto_wrap', '--tf32', 'True']
[2025-06-25 14:50:21,413] [INFO] [launch.py:256:main] process 838154 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/limo/bin/python3.10', '-u', 'train.py', '--local_rank=3', '--proctitle', 'junkim100', '--model_name_or_path', 'qwen2.5_7B', '--data_name', '/data_x/junkim100/projects/KULLM/finetune/data/code_switched_GAIR_LIMO_train_817.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'code-switch-limo', '--output_name', 'code-switch-limo', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-05', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--logging_steps', '1', '--fsdp', 'full_shard auto_wrap', '--tf32', 'True']
[2025-06-25 14:50:24,683] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-25 14:50:24,781] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-25 14:50:24,793] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-25 14:50:25,016] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-06-25 14:50:25,906] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
[2025-06-25 14:50:25,920] [INFO] [comm.py:675:init_distributed] cdb=None
[2025-06-25 14:50:25,920] [INFO] [comm.py:706:init_distributed] Initializing TorchBackend in DeepSpeed with backend nccl
[2025-06-25 14:50:26,024] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
[2025-06-25 14:50:26,026] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
[2025-06-25 14:50:26,040] [INFO] [comm.py:675:init_distributed] cdb=None
[2025-06-25 14:50:26,041] [INFO] [comm.py:675:init_distributed] cdb=None
WARNING:root:trining_args:
TrainingArguments(
_n_gpu=1,
accelerator_config={'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None, 'use_configured_state': False},
adafactor=False,
adam_beta1=0.9,
adam_beta2=0.999,
adam_epsilon=1e-08,
auto_find_batch_size=False,
average_tokens_across_devices=False,
batch_eval_metrics=False,
bf16=True,
bf16_full_eval=False,
cache_dir=None,
data_seed=None,
dataloader_drop_last=False,
dataloader_num_workers=0,
dataloader_persistent_workers=False,
dataloader_pin_memory=True,
dataloader_prefetch_factor=None,
ddp_backend=None,
ddp_broadcast_buffers=None,
ddp_bucket_cap_mb=None,
ddp_find_unused_parameters=None,
ddp_timeout=1800,
debug=[],
deepspeed=./deepspeed3.json,
disable_tqdm=False,
do_eval=False,
do_predict=False,
do_train=False,
eval_accumulation_steps=None,
eval_delay=0,
eval_do_concat_batches=True,
eval_on_start=False,
eval_steps=None,
eval_strategy=no,
eval_use_gather_object=False,
fp16=False,
fp16_backend=auto,
fp16_full_eval=False,
fp16_opt_level=O1,
fsdp=[],
fsdp_config={'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False},
fsdp_min_num_params=0,
fsdp_transformer_layer_cls_to_wrap=None,
full_determinism=False,
gradient_accumulation_steps=1,
gradient_checkpointing=False,
gradient_checkpointing_kwargs=None,
greater_is_better=None,
group_by_length=False,
half_precision_backend=auto,
hub_always_push=False,
hub_model_id=None,
hub_private_repo=None,
hub_strategy=every_save,
hub_token=<HUB_TOKEN>,
ignore_data_skip=False,
include_for_metrics=[],
include_inputs_for_metrics=False,
include_num_input_tokens_seen=False,
include_tokens_per_second=False,
jit_mode_eval=False,
label_names=None,
label_smoothing_factor=0.0,
learning_rate=1e-05,
length_column_name=length,
load_best_model_at_end=False,
local_rank=2,
log_level=passive,
log_level_replica=warning,
log_on_each_node=True,
logging_dir=trainer_output/runs/Jun25_14-50-24_nlp-server-17,
logging_first_step=False,
logging_nan_inf_filter=True,
logging_steps=1,
logging_strategy=steps,
lr_scheduler_kwargs={},
lr_scheduler_type=cosine,
max_grad_norm=0.3,
max_steps=-1,
metric_for_best_model=None,
mp_parameters=,
neftune_noise_alpha=None,
no_cuda=False,
num_train_epochs=2,
optim=adamw_torch,
optim_args=None,
optim_target_modules=None,
output_dir=trainer_output,
overwrite_output_dir=False,
past_index=-1,
per_device_eval_batch_size=1,
per_device_train_batch_size=1,
prediction_loss_only=False,
proctitle=junkim100,
push_to_hub=False,
push_to_hub_model_id=None,
push_to_hub_organization=None,
push_to_hub_token=<PUSH_TO_HUB_TOKEN>,
ray_scope=last,
remove_unused_columns=True,
report_to=[],
restore_callback_states_from_checkpoint=False,
resume_from_checkpoint=None,
run_name=trainer_output,
save_on_each_node=False,
save_only_model=True,
save_safetensors=True,
save_steps=500,
save_strategy=steps,
save_total_limit=None,
seed=42,
skip_memory_metrics=True,
tf32=True,
torch_compile=False,
torch_compile_backend=None,
torch_compile_mode=None,
torch_empty_cache_steps=None,
torchdynamo=None,
tpu_metrics_debug=False,
tpu_num_cores=None,
use_cpu=False,
use_ipex=False,
use_legacy_prediction_loop=False,
use_liger_kernel=False,
use_mps_device=False,
warmup_ratio=0.0,
warmup_steps=0,
wb_name=code-switch-limo,
wb_project=kullm-pro,
weight_decay=0.0,
)
WARNING:root:trining_args:
TrainingArguments(
_n_gpu=1,
accelerator_config={'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None, 'use_configured_state': False},
adafactor=False,
adam_beta1=0.9,
adam_beta2=0.999,
adam_epsilon=1e-08,
auto_find_batch_size=False,
average_tokens_across_devices=False,
batch_eval_metrics=False,
bf16=True,
bf16_full_eval=False,
cache_dir=None,
data_seed=None,
dataloader_drop_last=False,
dataloader_num_workers=0,
dataloader_persistent_workers=False,
dataloader_pin_memory=True,
dataloader_prefetch_factor=None,
ddp_backend=None,
ddp_broadcast_buffers=None,
ddp_bucket_cap_mb=None,
ddp_find_unused_parameters=None,
ddp_timeout=1800,
debug=[],
deepspeed=./deepspeed3.json,
disable_tqdm=False,
do_eval=False,
do_predict=False,
do_train=False,
eval_accumulation_steps=None,
eval_delay=0,
eval_do_concat_batches=True,
eval_on_start=False,
eval_steps=None,
eval_strategy=no,
eval_use_gather_object=False,
fp16=False,
fp16_backend=auto,
fp16_full_eval=False,
fp16_opt_level=O1,
fsdp=[],
fsdp_config={'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False},
fsdp_min_num_params=0,
fsdp_transformer_layer_cls_to_wrap=None,
full_determinism=False,
gradient_accumulation_steps=1,
gradient_checkpointing=False,
gradient_checkpointing_kwargs=None,
greater_is_better=None,
group_by_length=False,
half_precision_backend=auto,
hub_always_push=False,
hub_model_id=None,
hub_private_repo=None,
hub_strategy=every_save,
hub_token=<HUB_TOKEN>,
ignore_data_skip=False,
include_for_metrics=[],
include_inputs_for_metrics=False,
include_num_input_tokens_seen=False,
include_tokens_per_second=False,
jit_mode_eval=False,
label_names=None,
label_smoothing_factor=0.0,
learning_rate=1e-05,
length_column_name=length,
load_best_model_at_end=False,
local_rank=3,
log_level=passive,
log_level_replica=warning,
log_on_each_node=True,
logging_dir=trainer_output/runs/Jun25_14-50-24_nlp-server-17,
logging_first_step=False,
logging_nan_inf_filter=True,
logging_steps=1,
logging_strategy=steps,
lr_scheduler_kwargs={},
lr_scheduler_type=cosine,
max_grad_norm=0.3,
max_steps=-1,
metric_for_best_model=None,
mp_parameters=,
neftune_noise_alpha=None,
no_cuda=False,
num_train_epochs=2,
optim=adamw_torch,
optim_args=None,
optim_target_modules=None,
output_dir=trainer_output,
overwrite_output_dir=False,
past_index=-1,
per_device_eval_batch_size=1,
per_device_train_batch_size=1,
prediction_loss_only=False,
proctitle=junkim100,
push_to_hub=False,
push_to_hub_model_id=None,
push_to_hub_organization=None,
push_to_hub_token=<PUSH_TO_HUB_TOKEN>,
ray_scope=last,
remove_unused_columns=True,
report_to=[],
restore_callback_states_from_checkpoint=False,
resume_from_checkpoint=None,
run_name=trainer_output,
save_on_each_node=False,
save_only_model=True,
save_safetensors=True,
save_steps=500,
save_strategy=steps,
save_total_limit=None,
seed=42,
skip_memory_metrics=True,
tf32=True,
torch_compile=False,
torch_compile_backend=None,
torch_compile_mode=None,
torch_empty_cache_steps=None,
torchdynamo=None,
tpu_metrics_debug=False,
tpu_num_cores=None,
use_cpu=False,
use_ipex=False,
use_legacy_prediction_loop=False,
use_liger_kernel=False,
use_mps_device=False,
warmup_ratio=0.0,
warmup_steps=0,
wb_name=code-switch-limo,
wb_project=kullm-pro,
weight_decay=0.0,
)
[2025-06-25 14:50:26,447] [INFO] [config.py:655:__init__] Config mesh_device None world_size = 4
[2025-06-25 14:50:26,447] [INFO] [config.py:655:__init__] Config mesh_device None world_size = 4
[2025-06-25 14:50:26,655] [INFO] [logging.py:107:log_dist] [Rank -1] [TorchCheckpointEngine] Initialized with serialization = False
[2025-06-25 14:50:26,670] [INFO] [comm.py:675:init_distributed] cdb=None
WARNING:root:trining_args:
TrainingArguments(
_n_gpu=1,
accelerator_config={'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None, 'use_configured_state': False},
adafactor=False,
adam_beta1=0.9,
adam_beta2=0.999,
adam_epsilon=1e-08,
auto_find_batch_size=False,
average_tokens_across_devices=False,
batch_eval_metrics=False,
bf16=True,
bf16_full_eval=False,
cache_dir=None,
data_seed=None,
dataloader_drop_last=False,
dataloader_num_workers=0,
dataloader_persistent_workers=False,
dataloader_pin_memory=True,
dataloader_prefetch_factor=None,
ddp_backend=None,
ddp_broadcast_buffers=None,
ddp_bucket_cap_mb=None,
ddp_find_unused_parameters=None,
ddp_timeout=1800,
debug=[],
deepspeed=./deepspeed3.json,
disable_tqdm=False,
do_eval=False,
do_predict=False,
do_train=False,
eval_accumulation_steps=None,
eval_delay=0,
eval_do_concat_batches=True,
eval_on_start=False,
eval_steps=None,
eval_strategy=no,
eval_use_gather_object=False,
fp16=False,
fp16_backend=auto,
fp16_full_eval=False,
fp16_opt_level=O1,
fsdp=[],
fsdp_config={'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False},
fsdp_min_num_params=0,
fsdp_transformer_layer_cls_to_wrap=None,
full_determinism=False,
gradient_accumulation_steps=1,
gradient_checkpointing=False,
gradient_checkpointing_kwargs=None,
greater_is_better=None,
group_by_length=False,
half_precision_backend=auto,
hub_always_push=False,
hub_model_id=None,
hub_private_repo=None,
hub_strategy=every_save,
hub_token=<HUB_TOKEN>,
ignore_data_skip=False,
include_for_metrics=[],
include_inputs_for_metrics=False,
include_num_input_tokens_seen=False,
include_tokens_per_second=False,
jit_mode_eval=False,
label_names=None,
label_smoothing_factor=0.0,
learning_rate=1e-05,
length_column_name=length,
load_best_model_at_end=False,
local_rank=0,
log_level=passive,
log_level_replica=warning,
log_on_each_node=True,
logging_dir=trainer_output/runs/Jun25_14-50-24_nlp-server-17,
logging_first_step=False,
logging_nan_inf_filter=True,
logging_steps=1,
logging_strategy=steps,
lr_scheduler_kwargs={},
lr_scheduler_type=cosine,
max_grad_norm=0.3,
max_steps=-1,
metric_for_best_model=None,
mp_parameters=,
neftune_noise_alpha=None,
no_cuda=False,
num_train_epochs=2,
optim=adamw_torch,
optim_args=None,
optim_target_modules=None,
output_dir=trainer_output,
overwrite_output_dir=False,
past_index=-1,
per_device_eval_batch_size=1,
per_device_train_batch_size=1,
prediction_loss_only=False,
proctitle=junkim100,
push_to_hub=False,
push_to_hub_model_id=None,
push_to_hub_organization=None,
push_to_hub_token=<PUSH_TO_HUB_TOKEN>,
ray_scope=last,
remove_unused_columns=True,
report_to=[],
restore_callback_states_from_checkpoint=False,
resume_from_checkpoint=None,
run_name=trainer_output,
save_on_each_node=False,
save_only_model=True,
save_safetensors=True,
save_steps=500,
save_strategy=steps,
save_total_limit=None,
seed=42,
skip_memory_metrics=True,
tf32=True,
torch_compile=False,
torch_compile_backend=None,
torch_compile_mode=None,
torch_empty_cache_steps=None,
torchdynamo=None,
tpu_metrics_debug=False,
tpu_num_cores=None,
use_cpu=False,
use_ipex=False,
use_legacy_prediction_loop=False,
use_liger_kernel=False,
use_mps_device=False,
warmup_ratio=0.0,
warmup_steps=0,
wb_name=code-switch-limo,
wb_project=kullm-pro,
weight_decay=0.0,
)
WARNING:root:trining_args:
TrainingArguments(
_n_gpu=1,
accelerator_config={'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None, 'use_configured_state': False},
adafactor=False,
adam_beta1=0.9,
adam_beta2=0.999,
adam_epsilon=1e-08,
auto_find_batch_size=False,
average_tokens_across_devices=False,
batch_eval_metrics=False,
bf16=True,
bf16_full_eval=False,
cache_dir=None,
data_seed=None,
dataloader_drop_last=False,
dataloader_num_workers=0,
dataloader_persistent_workers=False,
dataloader_pin_memory=True,
dataloader_prefetch_factor=None,
ddp_backend=None,
ddp_broadcast_buffers=None,
ddp_bucket_cap_mb=None,
ddp_find_unused_parameters=None,
ddp_timeout=1800,
debug=[],
deepspeed=./deepspeed3.json,
disable_tqdm=False,
do_eval=False,
do_predict=False,
do_train=False,
eval_accumulation_steps=None,
eval_delay=0,
eval_do_concat_batches=True,
eval_on_start=False,
eval_steps=None,
eval_strategy=no,
eval_use_gather_object=False,
fp16=False,
fp16_backend=auto,
fp16_full_eval=False,
fp16_opt_level=O1,
fsdp=[],
fsdp_config={'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False},
fsdp_min_num_params=0,
fsdp_transformer_layer_cls_to_wrap=None,
full_determinism=False,
gradient_accumulation_steps=1,
gradient_checkpointing=False,
gradient_checkpointing_kwargs=None,
greater_is_better=None,
group_by_length=False,
half_precision_backend=auto,
hub_always_push=False,
hub_model_id=None,
hub_private_repo=None,
hub_strategy=every_save,
hub_token=<HUB_TOKEN>,
ignore_data_skip=False,
include_for_metrics=[],
include_inputs_for_metrics=False,
include_num_input_tokens_seen=False,
include_tokens_per_second=False,
jit_mode_eval=False,
label_names=None,
label_smoothing_factor=0.0,
learning_rate=1e-05,
length_column_name=length,
load_best_model_at_end=False,
local_rank=1,
log_level=passive,
log_level_replica=warning,
log_on_each_node=True,
logging_dir=trainer_output/runs/Jun25_14-50-24_nlp-server-17,
logging_first_step=False,
logging_nan_inf_filter=True,
logging_steps=1,
logging_strategy=steps,
lr_scheduler_kwargs={},
lr_scheduler_type=cosine,
max_grad_norm=0.3,
max_steps=-1,
metric_for_best_model=None,
mp_parameters=,
neftune_noise_alpha=None,
no_cuda=False,
num_train_epochs=2,
optim=adamw_torch,
optim_args=None,
optim_target_modules=None,
output_dir=trainer_output,
overwrite_output_dir=False,
past_index=-1,
per_device_eval_batch_size=1,
per_device_train_batch_size=1,
prediction_loss_only=False,
proctitle=junkim100,
push_to_hub=False,
push_to_hub_model_id=None,
push_to_hub_organization=None,
push_to_hub_token=<PUSH_TO_HUB_TOKEN>,
ray_scope=last,
remove_unused_columns=True,
report_to=[],
restore_callback_states_from_checkpoint=False,
resume_from_checkpoint=None,
run_name=trainer_output,
save_on_each_node=False,
save_only_model=True,
save_safetensors=True,
save_steps=500,
save_strategy=steps,
save_total_limit=None,
seed=42,
skip_memory_metrics=True,
tf32=True,
torch_compile=False,
torch_compile_backend=None,
torch_compile_mode=None,
torch_empty_cache_steps=None,
torchdynamo=None,
tpu_metrics_debug=False,
tpu_num_cores=None,
use_cpu=False,
use_ipex=False,
use_legacy_prediction_loop=False,
use_liger_kernel=False,
use_mps_device=False,
warmup_ratio=0.0,
warmup_steps=0,
wb_name=code-switch-limo,
wb_project=kullm-pro,
weight_decay=0.0,
)
[2025-06-25 14:50:26,945] [INFO] [config.py:655:__init__] Config mesh_device None world_size = 4
[2025-06-25 14:50:26,996] [INFO] [config.py:655:__init__] Config mesh_device None world_size = 4

Loading checkpoint shards:   0%|          | 0/7 [00:00<?, ?it/s][2025-06-25 14:50:34,790] [INFO] [partition_parameters.py:360:__exit__] finished initializing model - num_params = 339, num_elems = 7.62B

Loading checkpoint shards:   0%|          | 0/7 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/7 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/7 [00:00<?, ?it/s]
Loading checkpoint shards:  14%|█▍        | 1/7 [00:40<04:00, 40.11s/it]
Loading checkpoint shards:  14%|█▍        | 1/7 [00:39<03:59, 39.99s/it]
Loading checkpoint shards:  14%|█▍        | 1/7 [00:39<03:59, 40.00s/it]
Loading checkpoint shards:  14%|█▍        | 1/7 [00:40<04:00, 40.16s/it]
Loading checkpoint shards:  29%|██▊       | 2/7 [01:23<03:31, 42.23s/it]
Loading checkpoint shards:  29%|██▊       | 2/7 [01:23<03:30, 42.18s/it]
Loading checkpoint shards:  29%|██▊       | 2/7 [01:23<03:30, 42.18s/it]
Loading checkpoint shards:  29%|██▊       | 2/7 [01:23<03:31, 42.26s/it]
Loading checkpoint shards:  43%|████▎     | 3/7 [02:09<02:55, 43.94s/it]
Loading checkpoint shards:  43%|████▎     | 3/7 [02:09<02:55, 43.92s/it]
Loading checkpoint shards:  43%|████▎     | 3/7 [02:09<02:55, 43.91s/it]
Loading checkpoint shards:  43%|████▎     | 3/7 [02:09<02:55, 43.95s/it]
Loading checkpoint shards:  57%|█████▋    | 4/7 [02:53<02:11, 43.90s/it]
Loading checkpoint shards:  57%|█████▋    | 4/7 [02:53<02:11, 43.90s/it]
Loading checkpoint shards:  57%|█████▋    | 4/7 [02:53<02:11, 43.91s/it]
Loading checkpoint shards:  57%|█████▋    | 4/7 [02:53<02:11, 43.92s/it]
Loading checkpoint shards:  71%|███████▏  | 5/7 [03:37<01:28, 44.05s/it]
Loading checkpoint shards:  71%|███████▏  | 5/7 [03:37<01:28, 44.04s/it]
Loading checkpoint shards:  71%|███████▏  | 5/7 [03:37<01:28, 44.04s/it]
Loading checkpoint shards:  71%|███████▏  | 5/7 [03:38<01:28, 44.05s/it]
Loading checkpoint shards:  86%|████████▌ | 6/7 [04:10<00:40, 40.08s/it]
Loading checkpoint shards:  86%|████████▌ | 6/7 [04:10<00:40, 40.07s/it]
Loading checkpoint shards:  86%|████████▌ | 6/7 [04:10<00:40, 40.07s/it]
Loading checkpoint shards:  86%|████████▌ | 6/7 [04:10<00:40, 40.06s/it]
Loading checkpoint shards: 100%|██████████| 7/7 [04:29<00:00, 33.22s/it]
Loading checkpoint shards: 100%|██████████| 7/7 [04:29<00:00, 38.48s/it]

Loading checkpoint shards: 100%|██████████| 7/7 [04:29<00:00, 33.22s/it]
Loading checkpoint shards: 100%|██████████| 7/7 [04:29<00:00, 38.48s/it]

Loading checkpoint shards: 100%|██████████| 7/7 [04:29<00:00, 33.23s/it]
Loading checkpoint shards: 100%|██████████| 7/7 [04:29<00:00, 38.49s/it]

Loading checkpoint shards: 100%|██████████| 7/7 [04:29<00:00, 33.20s/it]
Loading checkpoint shards: 100%|██████████| 7/7 [04:29<00:00, 38.48s/it]
WARNING:root:Loading data...
[rank1]: Traceback (most recent call last):
[rank1]:   File "/data_x/junkim100/projects/KULLM/finetune/train.py", line 272, in <module>
[rank1]:     train(model_args, data_args, training_args)
[rank1]:   File "/data_x/junkim100/projects/KULLM/finetune/train.py", line 245, in train
[rank1]:     data_module = make_supervised_data_module(tokenizer=tokenizer, data_args=data_args)
[rank1]:   File "/data_x/junkim100/projects/KULLM/finetune/train.py", line 201, in make_supervised_data_module
[rank1]:     train_dataset = SupervisedDataset(
[rank1]:   File "/data_x/junkim100/projects/KULLM/finetune/train.py", line 157, in __init__
[rank1]:     data_all = load_from_disk(data_name)
[rank1]:   File "/mnt/raid6/junkim100/miniconda3/envs/limo/lib/python3.10/site-packages/datasets/load.py", line 2148, in load_from_disk
[rank1]:     raise FileNotFoundError(
[rank1]: FileNotFoundError: Directory /data_x/junkim100/projects/KULLM/finetune/data/code_switched_GAIR_LIMO_train_817.jsonl is neither a `Dataset` directory nor a `DatasetDict` directory.
WARNING:root:Loading data...
WARNING:root:Loading data...
[rank2]: Traceback (most recent call last):
[rank2]:   File "/data_x/junkim100/projects/KULLM/finetune/train.py", line 272, in <module>
[rank2]:     train(model_args, data_args, training_args)
[rank2]:   File "/data_x/junkim100/projects/KULLM/finetune/train.py", line 245, in train
[rank2]:     data_module = make_supervised_data_module(tokenizer=tokenizer, data_args=data_args)
[rank2]:   File "/data_x/junkim100/projects/KULLM/finetune/train.py", line 201, in make_supervised_data_module
[rank2]:     train_dataset = SupervisedDataset(
[rank2]:   File "/data_x/junkim100/projects/KULLM/finetune/train.py", line 157, in __init__
[rank2]:     data_all = load_from_disk(data_name)
[rank2]:   File "/mnt/raid6/junkim100/miniconda3/envs/limo/lib/python3.10/site-packages/datasets/load.py", line 2148, in load_from_disk
[rank2]:     raise FileNotFoundError(
[rank2]: FileNotFoundError: Directory /data_x/junkim100/projects/KULLM/finetune/data/code_switched_GAIR_LIMO_train_817.jsonl is neither a `Dataset` directory nor a `DatasetDict` directory.
[rank3]: Traceback (most recent call last):
[rank3]:   File "/data_x/junkim100/projects/KULLM/finetune/train.py", line 272, in <module>
[rank3]:     train(model_args, data_args, training_args)
[rank3]:   File "/data_x/junkim100/projects/KULLM/finetune/train.py", line 245, in train
[rank3]:     data_module = make_supervised_data_module(tokenizer=tokenizer, data_args=data_args)
[rank3]:   File "/data_x/junkim100/projects/KULLM/finetune/train.py", line 201, in make_supervised_data_module
[rank3]:     train_dataset = SupervisedDataset(
[rank3]:   File "/data_x/junkim100/projects/KULLM/finetune/train.py", line 157, in __init__
[rank3]:     data_all = load_from_disk(data_name)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/limo/lib/python3.10/site-packages/datasets/load.py", line 2148, in load_from_disk
[rank3]:     raise FileNotFoundError(
[rank3]: FileNotFoundError: Directory /data_x/junkim100/projects/KULLM/finetune/data/code_switched_GAIR_LIMO_train_817.jsonl is neither a `Dataset` directory nor a `DatasetDict` directory.
WARNING:root:Loading data...
[rank0]: Traceback (most recent call last):
[rank0]:   File "/data_x/junkim100/projects/KULLM/finetune/train.py", line 272, in <module>
[rank0]:     train(model_args, data_args, training_args)
[rank0]:   File "/data_x/junkim100/projects/KULLM/finetune/train.py", line 245, in train
[rank0]:     data_module = make_supervised_data_module(tokenizer=tokenizer, data_args=data_args)
[rank0]:   File "/data_x/junkim100/projects/KULLM/finetune/train.py", line 201, in make_supervised_data_module
[rank0]:     train_dataset = SupervisedDataset(
[rank0]:   File "/data_x/junkim100/projects/KULLM/finetune/train.py", line 157, in __init__
[rank0]:     data_all = load_from_disk(data_name)
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/limo/lib/python3.10/site-packages/datasets/load.py", line 2148, in load_from_disk
[rank0]:     raise FileNotFoundError(
[rank0]: FileNotFoundError: Directory /data_x/junkim100/projects/KULLM/finetune/data/code_switched_GAIR_LIMO_train_817.jsonl is neither a `Dataset` directory nor a `DatasetDict` directory.
[rank0]:[W625 14:55:05.065803975 ProcessGroupNCCL.cpp:1479] Warning: WARNING: destroy_process_group() was not called before program exit, which can leak resources. For more info, please see https://pytorch.org/docs/stable/distributed.html#shutdown (function operator())
[2025-06-25 14:55:11,735] [INFO] [launch.py:319:sigkill_handler] Killing subprocess 838151
[2025-06-25 14:55:11,789] [INFO] [launch.py:319:sigkill_handler] Killing subprocess 838152
[2025-06-25 14:55:11,811] [INFO] [launch.py:319:sigkill_handler] Killing subprocess 838153
[2025-06-25 14:55:11,831] [INFO] [launch.py:319:sigkill_handler] Killing subprocess 838154
[2025-06-25 14:55:11,831] [ERROR] [launch.py:325:sigkill_handler] ['/mnt/raid6/junkim100/miniconda3/envs/limo/bin/python3.10', '-u', 'train.py', '--local_rank=3', '--proctitle', 'junkim100', '--model_name_or_path', 'qwen2.5_7B', '--data_name', '/data_x/junkim100/projects/KULLM/finetune/data/code_switched_GAIR_LIMO_train_817.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'code-switch-limo', '--output_name', 'code-switch-limo', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-05', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--logging_steps', '1', '--fsdp', 'full_shard auto_wrap', '--tf32', 'True'] exits with return code = 1
