import os
import numpy as np
import argparse
from copy import deepcopy as copy

model_name = "qwen2.5_7B"
# data_name = "/data_x/junkim100/projects/KULLM/finetune/data/original_GAIR_LIMO_train_817.jsonl"
data_name = "/data_x/junkim100/projects/KULLM/finetune/data/code_switched_GAIR_LIMO_train_817.jsonl"
max_length = 16384

run_name = "code-switch-limo"

for lr in [1e-5]:
    for epoch in [2]:
        os.system(
            f"""
CUDA_VISIBLE_DEVICES=4,5,6,7 nohup deepspeed --num_gpus=4 --master_port=49056 train.py \
--proctitle junkim100 \
--model_name_or_path {model_name} \
--data_name '{data_name}' \
--wb_project "kullm-pro" \
--wb_name {run_name} \
--output_name {run_name} \
--max_length {max_length} \
--num_train_epochs {epoch} \
--per_device_train_batch_size 1 \
--per_device_eval_batch_size 1 \
--gradient_accumulation_steps 1 \
--save_only_model \
--learning_rate {lr} \
--weight_decay 0. \
--warmup_ratio 0. \
--lr_scheduler_type cosine \
--bf16 True \
--logging_steps 1 \
--fsdp "full_shard auto_wrap" \
--tf32 True > logs/train/{run_name}.log & 

wait
"""
        )

# nohup python train_run.py > logs/train &
